Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 00:42:55
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:26:48
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:26:48
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:26:49
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:26:51
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:26:57
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:26:57
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:27:12
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:27:14
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:27:17
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:27:23
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:27:31
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:27:33
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:27:38
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:27:41
----------------------------------------------------------------

Error in sending mail at method SendUserMail : Failure sending mail.
05/08/2025 10:27:56
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:27:59
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:28:00
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:28:02
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:28:25
----------------------------------------------------------------

Error in sending mail at method SendUserMail : Failure sending mail.
05/08/2025 10:28:35
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:28:38
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:29:20
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:29:24
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:29:26
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:29:27
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:31:50
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 10:32:06
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:10:44
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:11:02
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:11:15
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:11:16
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:11:32
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:11:35
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:11:50
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:11:51
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:11:53
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:11:54
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:12:05
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:12:08
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:12:09
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:12:15
----------------------------------------------------------------

Error in sending mail at method SendUserMail : Failure sending mail.
05/08/2025 11:12:20
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:12:23
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:12:33
----------------------------------------------------------------

Error in sending mail at method SendUserMail : Failure sending mail.
05/08/2025 11:12:43
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:12:46
----------------------------------------------------------------

Error occured in application Failed to get POHubAutoSourcing<br/>Cannot resolve the collation conflict between "SQL_Latin1_General_CP1_CI_AS" and "Latin1_General_CI_AS" in the equal to operation.<br />   at Rebound.GlobalTrader.DAL.SqlClient.SqlOfferProvider.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.DAL\SQLClient\SQLOfferProvider.cs:line 3172<br />   at Rebound.GlobalTrader.BLL.Offer.POHubAutoSourcing(Int32 customerRequirementId, Int32 sortIndex, Int32 sortDir, Int32 tableLength, Int32 clientNo, Int32 loginNo, String& ihsResult, String& lyticaResult) in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.BLL\Entities\Offer.cs:line 1846<br />   at Rebound.GlobalTrader.Site.Controls.Nuggets.Data.POHubSourcing.GetAutoSourcing() in C:\Users\<USER>\GTV1\Rebound_V1_Baseline_from_Release_18_August\GT_V1_Sourcecode\Rebound.GlobalTrader.Site\Controls\Nuggets\POHubSourcing\POHubSourcing.ashx.cs:line 2781
05/08/2025 11:12:47
----------------------------------------------------------------

