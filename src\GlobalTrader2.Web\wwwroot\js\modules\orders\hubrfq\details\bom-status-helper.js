/**
 * Helper module for fetching and updating BOM status from the server
 */
export class BOMStatusHelper {
    /**
     * Fetches the current BOM status from the server
     * @param {number} bomId - The BOM ID
     * @returns {Promise<string|null>} The current BOM status or null if failed
     */
    static async fetchBOMStatus(bomId) {
        try {
            const response = await fetch(`/api/orders/bom/${bomId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                console.error('Failed to fetch BOM status:', response.status, response.statusText);
                return null;
            }

            const result = await response.json();
            
            if (result.success && result.data && result.data.bomStatus) {
                return result.data.bomStatus;
            } else {
                console.error('Invalid response structure for BOM status:', result);
                return null;
            }
        } catch (error) {
            console.error('Error fetching BOM status:', error);
            return null;
        }
    }

    /**
     * Updates the BOM status element with the current server status
     * @param {number} bomId - The BOM ID
     * @param {string} fallbackStatus - Fallback status to use if server fetch fails
     * @returns {Promise<boolean>} True if successfully updated with server status, false if fallback was used
     */
    static async updateBOMStatusElement(bomId, fallbackStatus = '') {
        const statusElement = $('#bom-details-status');
        
        if (statusElement.length === 0) {
            console.warn('BOM status element not found');
            return false;
        }

        // Fetch current status from server
        const currentStatus = await this.fetchBOMStatus(bomId);
        
        if (currentStatus !== null) {
            // Successfully fetched from server
            statusElement.text(currentStatus);
            console.log('BOM status updated from server:', currentStatus);
            return true;
        } else {
            // Fallback to provided status
            if (fallbackStatus) {
                statusElement.text(fallbackStatus);
                console.warn('BOM status updated with fallback:', fallbackStatus);
            }
            return false;
        }
    }

    /**
     * Updates the BOM status element with a loading indicator, then fetches and updates with server status
     * @param {number} bomId - The BOM ID
     * @param {string} fallbackStatus - Fallback status to use if server fetch fails
     * @param {string} loadingText - Text to show while loading (default: 'Updating...')
     * @returns {Promise<boolean>} True if successfully updated with server status, false if fallback was used
     */
    static async updateBOMStatusWithLoading(bomId, fallbackStatus = '', loadingText = 'Updating...') {
        const statusElement = $('#bom-details-status');
        
        if (statusElement.length === 0) {
            console.warn('BOM status element not found');
            return false;
        }

        // Show loading indicator
        const originalText = statusElement.text();
        statusElement.text(loadingText);

        try {
            const result = await this.updateBOMStatusElement(bomId, fallbackStatus);
            return result;
        } catch (error) {
            console.error('Error updating BOM status:', error);
            // Restore original text on error
            statusElement.text(originalText);
            return false;
        }
    }
}
